/// ثوابت قاعدة البيانات - أسماء الجداول والحقول
/// Database Constants - Table and Column Names
class DatabaseConstants {
  // =========================
  // 🏢 جدول الفروع - Branches
  // =========================
  static const tableBranches = 'branches';
  static const columnBranchId = 'id';
  static const columnBranchName = 'name';
  static const columnBranchAddress = 'address';
  static const columnBranchPhone = 'phone';
  static const columnBranchEmail = 'email';
  static const columnBranchManagerId = 'manager_id';
  static const columnBranchIsActive = 'is_active';
  static const columnBranchCreatedAt = 'created_at';
  static const columnBranchUpdatedAt = 'updated_at';
  static const columnBranchDeletedAt = 'deleted_at';
  static const columnBranchIsSynced = 'is_synced';

  // =========================
  // 👥 جدول المستخدمين - Users
  // =========================
  static const tableUsers = 'users';
  static const columnUserId = 'id';
  static const columnUserName = 'name';
  static const columnUserEmail = 'email';
  static const columnUserPhone = 'phone';
  static const columnUserRole = 'role';
  static const columnUserPermissions = 'permissions';
  static const columnUserIsActive = 'is_active';
  static const columnUserProfileImage = 'profile_image';
  static const columnUserPasswordHash = 'password_hash';
  static const columnUserCreatedAt = 'created_at';
  static const columnUserUpdatedAt = 'updated_at';
  static const columnUserLastLoginAt = 'last_login_at';
  static const columnUserBranchId = 'branch_id';
  static const columnUserDeletedAt = 'deleted_at';
  static const columnUserIsSynced = 'is_synced';

  // =========================
  // 📂 جدول الفئات - Categories
  // =========================
  static const tableCategories = 'categories';
  static const columnCategoryId = 'id';
  static const columnCategoryNameAr = 'name_ar';
  static const columnCategoryNameEn = 'name_en';
  static const columnCategoryDescription = 'description';
  static const columnCategoryParentId = 'parent_id';
  static const columnCategoryCreatedAt = 'created_at';
  static const columnCategoryUpdatedAt = 'updated_at';
  static const columnCategoryDeletedAt = 'deleted_at';
  static const columnCategoryIsSynced = 'is_synced';

// الأعمدة الإضافية
  static const columnCategoryImageUrl = 'image_url';
  static const columnCategorySortOrder = 'sort_order';
  static const columnCategoryIsActive = 'is_active';
  static const columnCategoryTaxRate = 'tax_rate';
  static const columnCategoryDiscountRate = 'discount_rate';
  static const columnCategoryIconName = 'icon_name';
  static const columnCategoryColor = 'color';
  static const columnCategoryTags = 'tags';
  static const columnCategoryCustomFieds = 'custom_fields';
  // =========================
  // 📏 جدول الوحدات - Units
  // =========================
  static const tableUnits = 'units';
  static const columnUnitId = 'id';
  static const columnUnitName = 'name';

  static const columnUnitSymbol = 'symbol';
  static const columnUnitAbbreviation = 'abbreviation';
  static const columnUnitFactor = 'factor';
  static const columnUnitBaseUnitId = 'base_unit_id';
  static const columnUnitType = 'unit_type';
  static const columnUnitIsBaseUnit = 'is_base_unit';
  static const columnUnitIsActive = 'is_active';
  static const columnUnitDescription = 'description';
  static const columnUnitCreatedAt = 'created_at';
  static const columnUnitUpdatedAt = 'updated_at';
  static const columnUnitDeletedAt = 'deleted_at';
  static const columnUnitIsSynced = 'is_synced';
  // static const String columnUnitNameEn = 'unit_name_en';
  static const String columnUnitUsageCount = 'usage_count';
  static const String columnUnitBranchCount = 'branch_count';
  static const String columnUnitCategoryCount = 'category_count';
  // =========================
  // 📦 جدول المنتجات - Products
  // =========================
  static const tableProducts = 'products';
  static const columnProductId = 'id';
  static const columnProductNameAr = 'name_ar';
  static const columnProductNameEn = 'name_en';
  static const columnProductBarcode = 'barcode';
  static const columnProductDescription = 'description';
  static const columnProductCategoryId = 'category_id';
  static const columnProductBaseUnitId = 'prodect_base_unit_id';
  static const columnProductCostPrice = 'cost_price';
  static const columnProductSellingPrice = 'selling_price';
  static const columnProductMinStock = 'min_stock';
  static const columnProductMaxStock = 'max_stock';
  static const columnProductReorderPoint = 'reorder_point';
  static const columnProductTrackStock = 'track_stock';
  static const columnProductIsActive = 'is_active';
  static const columnProductImageUrl = 'image_url';
  static const columnProductCreatedAt = 'created_at';
  static const columnProductUpdatedAt = 'updated_at';
  static const columnProductDeletedAt = 'deleted_at';
  static const columnProductIsSynced = 'is_synced';

// =========================
// 📦 جدول المنتجات – Products (إضافات)
// =========================
  static const columnProductQrCode = 'qr_code';
  static const columnProductAllowNegativeStock = 'allow_negative_stock';
  static const columnProductHasExpiryDate = 'has_expiry_date';
  static const columnProductExpiryDate = 'expiry_date';
  static const columnProductNotes = 'notes';
  static const columnProductWeight = 'weight';
  static const columnProductVolume = 'volume';
  static const columnProductSku = 'sku';
  static const columnProductManufacturerCode = 'manufacturer_code';
  static const columnProductSupplierCode = 'supplier_code';
  static const columnProductTags = 'tags';
  // =========================
  // 🏪 جدول المستودعات - Warehouses
  // =========================
  static const tableWarehouses = 'warehouses';
  static const columnWarehouseId = 'id';
  static const columnWarehouseName = 'name';
  static const columnWarehouseLocation = 'location';
  static const columnWarehouseDescription = 'description';
  static const columnWarehouseCode = 'code';

  static const columnWarehouseManagerId = 'manager_id';
  static const columnWarehouseIsActive = 'is_active';
  static const columnWarehouseCreatedAt = 'created_at';
  static const columnWarehouseUpdatedAt = 'updated_at';
  static const columnWarehouseDeletedAt = 'deleted_at';
  static const columnWarehouseBranchId = 'branch_id';
  static const columnWarehouseIsSynced = 'is_synced';

  // =========================
  // 📊 جدول المخزون - Stocks
  // =========================
  static const tableStocks = 'stocks';
  static const columnStockId = 'id';
  static const columnStockProductId = 'product_id';
  static const columnStockWarehouseId = 'warehouse_id';
  static const columnStockQuantity = 'quantity';
  static const columnStockCreatedAt = 'created_at';
  static const columnStockUpdatedAt = 'updated_at';
  static const columnStockDeletedAt = 'deleted_at';
  static const columnStockIsSynced = 'is_synced';

  // =========================
  // 🔄 جدول حركات المخزون - Stock Movements
  // =========================
  static const tableStockMovements = 'stock_movements';
  static const columnStockMovementId = 'id';
  static const columnStockMovementProductId = 'product_id';
  static const columnStockMovementWarehouseId = 'warehouse_id';
  static const columnStockMovementType = 'movement_type';
  static const columnStockMovementQuantity = 'quantity';
  static const columnStockMovementQty =
      'qty'; // Legacy - keep for compatibility
  static const columnStockMovementUnitCost = 'unit_cost';
  static const columnStockMovementTotalValue = 'total_value';
  static const columnStockMovementUnitId = 'unit_id';
  static const columnStockMovementReferenceType = 'reference_type';
  static const columnStockMovementReferenceId = 'reference_id';
  static const columnStockMovementNotes = 'notes';
  static const columnStockMovementCreatedBy = 'created_by';
  static const columnStockMovementBranchId = 'branch_id';
  static const columnStockMovementCreatedAt = 'created_at';
  static const columnStockMovementUpdatedAt = 'updated_at';
  static const columnStockMovementDeletedAt = 'deleted_at';
  static const columnStockMovementIsSynced = 'is_synced';

  // =========================
  // 👥 جدول العملاء - Customers
  // =========================
  static const tableCustomers = 'customers';
  static const columnCustomerId = 'id';
  static const columnCustomerName = 'name';
  static const columnCustomerPhone = 'phone';
  static const columnCustomerEmail = 'email';
  static const columnCustomerAddress = 'address';
  static const columnCustomerBalance = 'balance';
  static const columnCustomerNotes = 'notes';
  static const columnCustomerCreatedAt = 'created_at';
  static const columnCustomerUpdatedAt = 'updated_at';
  static const columnCustomerDeletedAt = 'deleted_at';
  static const columnCustomerIsSynced = 'is_synced';

  // =========================
  // 🏭 جدول الموردين - Suppliers
  // =========================
  static const tableSuppliers = 'suppliers';
  static const columnSupplierId = 'id';
  static const columnSupplierName = 'name';
  static const columnSupplierPhone = 'phone';
  static const columnSupplierEmail = 'email';
  static const columnSupplierAddress = 'address';
  static const columnSupplierBalance = 'balance';
  static const columnSupplierNotes = 'notes';
  static const columnSupplierCreatedAt = 'created_at';
  static const columnSupplierUpdatedAt = 'updated_at';
  static const columnSupplierDeletedAt = 'deleted_at';
  static const columnSupplierIsSynced = 'is_synced';

  // =========================
  // 💰 جدول المبيعات - Sales
  // =========================
  static const tableSales = 'sales';
  static const columnSaleId = 'id';
  static const columnSaleCustomerId = 'customer_id';
  static const columnSaleInvoiceNo = 'invoice_no';
  static const columnSaleBranchId = 'branch_id';
  static const columnSaleSaleDate = 'sale_date';
  static const columnSaleTotalAmount = 'total_amount';
  static const columnSalePaidAmount = 'paid_amount';
  static const columnSaleDueAmount = 'due_amount';
  static const columnSaleNotes = 'notes';
  static const columnSaleCreatedAt = 'created_at';
  static const columnSaleUpdatedAt = 'updated_at';
  static const columnSaleDeletedAt = 'deleted_at';
  static const columnSaleIsSynced = 'is_synced';

  // =========================
  // 📋 جدول عناصر المبيعات - Sale Items
  // =========================
  static const tableSaleItems = 'sale_items';
  static const columnSaleItemId = 'id';
  static const columnSaleItemSaleId = 'sale_id';
  static const columnSaleItemProductId = 'product_id';
  static const columnSaleItemUnitId = 'unit_id';
  static const columnSaleItemQty = 'qty';
  static const columnSaleItemUnitPrice = 'unit_price';
  static const columnSaleItemTotalPrice = 'total_price';
  static const columnSaleItemCreatedAt = 'created_at';
  static const columnSaleItemUpdatedAt = 'updated_at';
  static const columnSaleItemDeletedAt = 'deleted_at';
  static const columnSaleItemIsSynced = 'is_synced';

  // =========================
  // 🛒 جدول المشتريات - Purchases
  // =========================
  static const tablePurchases = 'purchases';
  static const columnPurchaseId = 'id';
  static const columnPurchaseSupplierId = 'supplier_id';
  static const columnPurchaseInvoiceNo = 'invoice_no';
  static const columnPurchaseBranchId = 'branch_id';
  static const columnPurchasePurchaseDate = 'purchase_date';
  static const columnPurchaseTotalAmount = 'total_amount';
  static const columnPurchasePaidAmount = 'paid_amount';
  static const columnPurchaseDueAmount = 'due_amount';
  static const columnPurchaseNotes = 'notes';
  static const columnPurchaseCreatedAt = 'created_at';
  static const columnPurchaseUpdatedAt = 'updated_at';
  static const columnPurchaseDeletedAt = 'deleted_at';
  static const columnPurchaseIsSynced = 'is_synced';

  // =========================
  // 📋 جدول عناصر المشتريات - Purchase Items
  // =========================
  static const tablePurchaseItems = 'purchase_items';
  static const columnPurchaseItemId = 'id';
  static const columnPurchaseItemPurchaseId = 'purchase_id';
  static const columnPurchaseItemProductId = 'product_id';
  static const columnPurchaseItemUnitId = 'unit_id';
  static const columnPurchaseItemQty = 'qty';
  static const columnPurchaseItemUnitPrice = 'unit_price';
  static const columnPurchaseItemTotalPrice = 'total_price';
  static const columnPurchaseItemCreatedAt = 'created_at';
  static const columnPurchaseItemUpdatedAt = 'updated_at';
  static const columnPurchaseItemDeletedAt = 'deleted_at';
  static const columnPurchaseItemIsSynced = 'is_synced';

  // =========================
  // 💵 جدول الصناديق - Cash Boxes
  // =========================
  static const tableCashBoxes = 'cash_boxes';
  static const columnCashBoxId = 'id';
  static const columnCashBoxName = 'name';
  static const columnCashBoxBalance = 'balance';
  static const columnCashBoxIsActive = 'is_active';
  static const columnCashBoxCreatedAt = 'created_at';
  static const columnCashBoxUpdatedAt = 'updated_at';
  static const columnCashBoxDeletedAt = 'deleted_at';
  static const columnCashBoxBranchId = 'branch_id';
  static const columnCashBoxIsSynced = 'is_synced';

  // =========================
  // 🏦 جدول الحسابات البنكية - Bank Accounts
  // =========================
  static const tableBankAccounts = 'bank_accounts';
  static const columnBankAccountId = 'id';
  static const columnBankAccountBankName = 'bank_name';
  static const columnBankAccountAccountNumber = 'account_number';
  static const columnBankAccountAccountName = 'account_name';
  static const columnBankAccountIban = 'iban';
  static const columnBankAccountBalance = 'balance';
  static const columnBankAccountIsActive = 'is_active';
  static const columnBankAccountCreatedAt = 'created_at';
  static const columnBankAccountUpdatedAt = 'updated_at';
  static const columnBankAccountDeletedAt = 'deleted_at';
  static const columnBankAccountBranchId = 'branch_id';
  static const columnBankAccountIsSynced = 'is_synced';

  // =========================
  // 💳 جدول المعاملات - Transactions
  // =========================
  static const tableTransactions = 'transactions';
  static const columnTransactionId = 'id';
  static const columnTransactionType = 'type';
  static const columnTransactionSubType = 'sub_type';
  static const columnTransactionAmount = 'amount';
  static const columnTransactionCustomerId = 'customer_id';
  static const columnTransactionSupplierId = 'supplier_id';
  static const columnTransactionCashBoxId = 'cash_box_id';
  static const columnTransactionBankAccountId = 'bank_account_id';
  static const columnTransactionTransactionDate = 'transaction_date';
  static const columnTransactionNotes = 'notes';
  static const columnTransactionCreatedAt = 'created_at';
  static const columnTransactionUpdatedAt = 'updated_at';
  static const columnTransactionDeletedAt = 'deleted_at';
  static const columnTransactionIsSynced = 'is_synced';

  // =========================
  // 🔔 جدول الإشعارات - Notifications
  // =========================
  static const tableNotifications = 'notifications';
  static const columnNotificationId = 'id';
  static const columnNotificationUserId = 'user_id';
  static const columnNotificationTitle = 'title';
  static const columnNotificationBody = 'body';
  static const columnNotificationType = 'type';
  static const columnNotificationIsRead = 'is_read';
  static const columnNotificationMetadata = 'metadata';
  static const columnNotificationCreatedAt = 'created_at';
  static const columnNotificationReadAt = 'read_at';
  static const columnNotificationDeletedAt = 'deleted_at';
  static const columnNotificationIsSynced = 'is_synced';

  // =========================
  // 📱 جدول جلسات الأجهزة - Device Sessions
  // =========================
  static const tableDeviceSessions = 'device_sessions';
  static const columnDeviceSessionId = 'id';
  static const columnDeviceSessionUserId = 'user_id';
  static const columnDeviceSessionDeviceName = 'device_name';
  static const columnDeviceSessionDeviceOs = 'device_os';
  static const columnDeviceSessionAppVersion = 'app_version';
  static const columnDeviceSessionIpAddress = 'ip_address';
  static const columnDeviceSessionToken = 'token';
  static const columnDeviceSessionIsActive = 'is_active';
  static const columnDeviceSessionLastLoginAt = 'last_login_at';
  static const columnDeviceSessionCreatedAt = 'created_at';
  static const columnDeviceSessionUpdatedAt = 'updated_at';
  static const columnDeviceSessionDeletedAt = 'deleted_at';
  static const columnDeviceSessionIsSynced = 'is_synced';

  // =========================
  // 📎 جدول المرفقات - Attachments
  // =========================
  static const tableAttachments = 'attachments';
  static const columnAttachmentId = 'id';
  static const columnAttachmentReferenceType = 'reference_type';
  static const columnAttachmentReferenceId = 'reference_id';
  static const columnAttachmentFileName = 'file_name';
  static const columnAttachmentFilePath = 'file_path';
  static const columnAttachmentMimeType = 'mime_type';
  static const columnAttachmentSize = 'size';
  static const columnAttachmentUploadedBy = 'uploaded_by';
  static const columnAttachmentCreatedAt = 'created_at';
  static const columnAttachmentUpdatedAt = 'updated_at';
  static const columnAttachmentDeletedAt = 'deleted_at';
  static const columnAttachmentIsSynced = 'is_synced';

  // =========================
  // ⚙️ جدول الإعدادات - Settings
  // =========================
  static const tableSettings = 'settings';
  static const columnSettingKey = 'key';
  static const columnSettingValue = 'value';
  static const columnSettingType = 'type';
  static const columnSettingDescription = 'description';
  static const columnSettingUpdatedAt = 'updated_at';
  static const columnSettingIsSynced = 'is_synced';

  // =========================
  // 🔄 جدول سجل المزامنة - Sync Log
  // =========================
  static const tableSyncLog = 'sync_log';
  static const columnSyncLogId = 'id';
  static const columnSyncLogTableName = 'table_name';
  static const columnSyncLogOperation = 'operation';
  static const columnSyncLogRecordId = 'record_id';
  static const columnSyncLogStatus = 'status';
  static const columnSyncLogErrorMessage = 'error_message';
  static const columnSyncLogCreatedAt = 'created_at';
  static const columnSyncLogSyncedAt = 'synced_at';

  // =========================
  // 🤖 جدول توقعات الذكاء الاصطناعي - AI Predictions
  // =========================
  static const tableAiPredictions = 'ai_predictions';
  static const columnAiPredictionId = 'id';
  static const columnAiPredictionType = 'type';
  static const columnAiPredictionPeriod = 'period';
  static const columnAiPredictionPredictionDate = 'prediction_date';
  static const columnAiPredictionTargetDate = 'target_date';
  static const columnAiPredictionInputData = 'input_data';
  static const columnAiPredictionPredictions = 'predictions';
  static const columnAiPredictionConfidence = 'confidence';
  static const columnAiPredictionAlgorithm = 'algorithm';
  static const columnAiPredictionMetadata = 'metadata';
  static const columnAiPredictionCreatedAt = 'created_at';

  // =========================
  // 💡 جدول رؤى الذكاء الاصطناعي - AI Insights
  // =========================
  static const tableAiInsights = 'ai_insights';
  static const columnAiInsightId = 'id';
  static const columnAiInsightType = 'type';
  static const columnAiInsightTitle = 'title';
  static const columnAiInsightDescription = 'description';
  static const columnAiInsightData = 'data';
  static const columnAiInsightPriority = 'priority';
  static const columnAiInsightStatus = 'status';
  static const columnAiInsightCreatedAt = 'created_at';
  static const columnAiInsightUpdatedAt = 'updated_at';

  // =========================
  // 📋 جدول سجل المراجعة - Audit Log
  // =========================
  static const tableAuditLog = 'audit_log';
  static const columnAuditLogId = 'id';
  static const columnAuditLogUserId = 'user_id';
  static const columnAuditLogAction = 'action';
  static const columnAuditLogTableName = 'table_name';
  static const columnAuditLogRecordId = 'record_id';
  static const columnAuditLogOldValues = 'old_values';
  static const columnAuditLogNewValues = 'new_values';
  static const columnAuditLogIpAddress = 'ip_address';
  static const columnAuditLogUserAgent = 'user_agent';
  static const columnAuditLogCreatedAt = 'created_at';
  static const columnAuditLogIsSynced = 'is_synced';

  // =========================
  // 💰 جدول أسعار المنتجات - Product Prices
  // =========================
  static const tableProductPrices = 'product_prices';
  static const columnProductPriceId = 'id';
  static const columnProductPriceProductId = 'product_id';
  static const columnProductPriceUnitId = 'unit_id';
  static const columnProductPricePurchasePrice = 'purchase_price';
  static const columnProductPriceSalePrice = 'sale_price';
  static const columnProductPriceCreatedAt = 'created_at';
  static const columnProductPriceUpdatedAt = 'updated_at';
  static const columnProductPriceDeletedAt = 'deleted_at';
  static const columnProductPriceIsSynced = 'is_synced';

  // =========================
  // 📊 جدول تسويات المخزون - Stock Adjustments
  // =========================
  static const tableStockAdjustments = 'stock_adjustments';
  static const columnStockAdjustmentId = 'id';
  static const columnStockAdjustmentReferenceNumber = 'reference_number';
  static const columnStockAdjustmentDate = 'adjustment_date';
  static const columnStockAdjustmentWarehouseId = 'warehouse_id';
  static const columnStockAdjustmentReason = 'reason';
  static const columnStockAdjustmentNotes = 'notes';
  static const columnStockAdjustmentStatus = 'status';
  static const columnStockAdjustmentTotalValue = 'total_value';
  static const columnStockAdjustmentCreatedBy = 'created_by';
  static const columnStockAdjustmentApprovedBy = 'approved_by';
  static const columnStockAdjustmentApprovedAt = 'approved_at';
  static const columnStockAdjustmentCreatedAt = 'created_at';
  static const columnStockAdjustmentUpdatedAt = 'updated_at';
  static const columnStockAdjustmentDeletedAt = 'deleted_at';
  static const columnStockAdjustmentBranchId = 'branch_id';
  static const columnStockAdjustmentIsSynced = 'is_synced';

  // =========================
  // 📋 جدول عناصر تسويات المخزون - Stock Adjustment Items
  // =========================
  static const tableStockAdjustmentItems = 'stock_adjustment_items';
  static const columnStockAdjustmentItemId = 'id';
  static const columnStockAdjustmentItemAdjustmentId = 'adjustment_id';
  static const columnStockAdjustmentItemProductId = 'product_id';
  static const columnStockAdjustmentItemCurrentQuantity = 'current_quantity';
  static const columnStockAdjustmentItemAdjustmentQuantity =
      'adjustment_quantity';
  static const columnStockAdjustmentItemNewQuantity = 'new_quantity';
  static const columnStockAdjustmentItemType = 'adjustment_type';
  static const columnStockAdjustmentItemReason = 'reason';
  static const columnStockAdjustmentItemUnitCost = 'unit_cost';
  static const columnStockAdjustmentItemTotalValue = 'total_value';
  static const columnStockAdjustmentItemNotes = 'notes';
  static const columnStockAdjustmentItemCreatedAt = 'created_at';
  static const columnStockAdjustmentItemUpdatedAt = 'updated_at';
  static const columnStockAdjustmentItemDeletedAt = 'deleted_at';
  static const columnStockAdjustmentItemIsSynced = 'is_synced';

  // =========================
  // 👨‍💼 جدول الموظفين - Employees
  // =========================
  static const tableEmployees = 'employees';
  static const columnEmployeeId = 'id';
  static const columnEmployeeName = 'name';
  static const columnEmployeePhone = 'phone';
  static const columnEmployeeJobTitle = 'job_title';
  static const columnEmployeeSalaryFixed = 'salary_fixed';
  static const columnEmployeeNotes = 'notes';
  static const columnEmployeeCreatedAt = 'created_at';
  static const columnEmployeeUpdatedAt = 'updated_at';
  static const columnEmployeeDeletedAt = 'deleted_at';
  static const columnEmployeeIsSynced = 'is_synced';

  // =========================
  // 💰 جدول الرواتب - Salaries
  // =========================
  static const tableSalaries = 'salaries';
  static const columnSalaryId = 'id';
  static const columnSalaryEmployeeId = 'employee_id';
  static const columnSalaryMonth = 'month';
  static const columnSalaryBaseSalary = 'base_salary';
  static const columnSalaryBonus = 'bonus';
  static const columnSalaryAdvance = 'advance';
  static const columnSalaryDeductions = 'deductions';
  static const columnSalaryNetSalary = 'net_salary';
  static const columnSalaryPaid = 'paid';
  static const columnSalaryDate = 'date';
  static const columnSalaryCreatedAt = 'created_at';
  static const columnSalaryUpdatedAt = 'updated_at';
  static const columnSalaryDeletedAt = 'deleted_at';
  static const columnSalaryIsSynced = 'is_synced';

  // =========================
  // 📒 جدول القيود اليومية - Journal Entries
  // =========================
  static const tableJournalEntries = 'journal_entries';
  static const columnJournalEntryId = 'id';
  static const columnJournalEntryDebitAccount = 'debit_account';
  static const columnJournalEntryCreditAccount = 'credit_account';
  static const columnJournalEntryAmount = 'amount';
  static const columnJournalEntryDate = 'date';
  static const columnJournalEntryNotes = 'notes';
  static const columnJournalEntryCreatedAt = 'created_at';
  static const columnJournalEntryUpdatedAt = 'updated_at';
  static const columnJournalEntryDeletedAt = 'deleted_at';
  static const columnJournalEntryIsSynced = 'is_synced';
}
