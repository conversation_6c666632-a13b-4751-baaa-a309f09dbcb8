import 'dart:io';

import 'package:flutter/material.dart';
import 'package:tijari_tech/services/financial_reports_export.dart';

void main() async {
  print('🧪 اختبار نظام التصدير...');

  try {
    // بيانات تجريبية للمبيعات
    final salesData = [
      {
        'invoice_no': 'S001',
        'sale_date': '2024-01-15',
        'customer_name': 'أحمد محمد',
        'total': 1500.0,
        'paid': 1000.0,
        'due': 500.0,
      },
      {
        'invoice_no': 'S002',
        'sale_date': '2024-01-16',
        'customer_name': 'فاطمة علي',
        'total': 2500.0,
        'paid': 2500.0,
        'due': 0.0,
      },
    ];

    // بيانات تجريبية للمشتريات
    final purchasesData = [
      {
        'invoice_no': 'P001',
        'purchase_date': '2024-01-10',
        'supplier_name': 'شركة التوريد المحدودة',
        'total_amount': 3000.0,
        'paid_amount': 2000.0,
        'due_amount': 1000.0,
      },
    ];

    // إحصائيات المبيعات
    final salesStats = {
      'totalSales': 2,
      'totalAmount': 4000.0,
      'totalPaid': 3500.0,
      'totalDue': 500.0,
    };

    // إحصائيات المشتريات
    final purchasesStats = {
      'totalPurchases': 1,
      'totalAmount': 3000.0,
      'totalPaid': 2000.0,
      'totalDue': 1000.0,
    };

    // نطاق التاريخ
    final dateRange = DateTimeRange(
      start: DateTime(2024, 1, 1),
      end: DateTime(2024, 1, 31),
    );

    print('📊 اختبار تصدير تقرير المبيعات والمشتريات...');
    final salesPurchasesFile =
        await FinancialReportsExport.exportSalesPurchasesToExcel(
      sales: salesData,
      purchases: purchasesData,
      salesStats: salesStats,
      purchasesStats: purchasesStats,
      dateRange: dateRange,
    );
    print('✅ تم إنشاء ملف المبيعات والمشتريات: $salesPurchasesFile');

    // بيانات الأرباح والخسائر
    final profitLossData = {
      'totalRevenue': 4000.0,
      'totalCOGS': 2500.0,
      'grossProfit': 1500.0,
      'grossProfitMargin': 37.5,
      'operatingExpenses': 500.0,
      'operatingProfit': 1000.0,
      'netProfit': 1000.0,
    };

    print('💰 اختبار تصدير تقرير الأرباح والخسائر...');
    final profitLossFile = await FinancialReportsExport.exportProfitLossToExcel(
      profitLossData: profitLossData,
      dateRange: dateRange,
    );
    print('✅ تم إنشاء ملف الأرباح والخسائر: $profitLossFile');

    // بيانات العملاء والموردين
    final customersData = [
      {
        'name': 'أحمد محمد',
        'totalSales': 1500.0,
        'totalPaid': 1000.0,
        'totalDue': 500.0,
        'salesCount': 1,
      },
      {
        'name': 'فاطمة علي',
        'totalSales': 2500.0,
        'totalPaid': 2500.0,
        'totalDue': 0.0,
        'salesCount': 1,
      },
    ];

    final suppliersData = [
      {
        'name': 'شركة التوريد المحدودة',
        'totalPurchases': 3000.0,
        'totalPaid': 2000.0,
        'totalDue': 1000.0,
        'purchasesCount': 1,
      },
    ];

    final accountsSummary = {
      'totalCustomers': 2,
      'activeCustomers': 2,
      'totalCustomerSales': 4000.0,
      'totalCustomerDue': 500.0,
      'totalSuppliers': 1,
      'activeSuppliers': 1,
      'totalSupplierPurchases': 3000.0,
      'totalSupplierDue': 1000.0,
    };

    print('👥 اختبار تصدير تقرير العملاء والموردين...');
    final accountsFile = await FinancialReportsExport.exportAccountsToExcel(
      customers: customersData,
      suppliers: suppliersData,
      accountsSummary: accountsSummary,
    );
    print('✅ تم إنشاء ملف العملاء والموردين: $accountsFile');

    // بيانات لوحة التحكم
    final dashboardData = {
      'totalRevenue': 4000.0,
      'totalExpenses': 3000.0,
      'netProfit': 1000.0,
      'profitMargin': 25.0,
      'totalSales': 2,
      'totalPurchases': 1,
      'totalCustomers': 2,
      'totalSuppliers': 1,
    };

    final recentTransactions = [
      {
        'type': 'sale',
        'description': 'فاتورة مبيعات S002',
        'amount': 2500.0,
        'date': '2024-01-16',
      },
      {
        'type': 'purchase',
        'description': 'فاتورة مشتريات P001',
        'amount': 3000.0,
        'date': '2024-01-10',
      },
    ];

    print('📋 اختبار تصدير لوحة التحكم إلى PDF...');
    final dashboardFile = await FinancialReportsExport.exportDashboardToPDF(
      dashboardData: dashboardData,
      recentTransactions: recentTransactions,
    );
    print('✅ تم إنشاء ملف لوحة التحكم: $dashboardFile');

    print('\n🎉 تم اختبار جميع وظائف التصدير بنجاح!');
    print('📁 تم حفظ الملفات في مجلد المستندات');
  } catch (e, stackTrace) {
    print('❌ حدث خطأ أثناء الاختبار: $e');
    print('📍 تفاصيل الخطأ: $stackTrace');
  }
}
