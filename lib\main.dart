import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:sqflite/sqflite.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:tijari_tech/data/local/delete_db.dart';

import 'app.dart';
import 'core/auth/auth_service.dart';
import 'core/utils/app_utils.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة sqflite للعمل على سطح المكتب
  if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;
  }

  // تهيئة قاعدة البيانات
  try {
    await initializeDatabase();
    AppUtils.logInfo('تم تهيئة قاعدة البيانات بنجاح');
  } catch (e) {
    AppUtils.logError('خطأ في تهيئة قاعدة البيانات', e);

    // محاولة إعادة تعيين قاعدة البيانات
    try {
      AppUtils.logInfo('محاولة إعادة تعيين قاعدة البيانات...');
      await deleteDatabaseFile();
      await initializeDatabase();
      AppUtils.logInfo('تم إعادة تعيين قاعدة البيانات بنجاح');
    } catch (resetError) {
      AppUtils.logError('فشل في إعادة تعيين قاعدة البيانات', resetError);
    }
  }
  // تهيئة خدمة المصادقة
  // await AuthService.init();

  runApp(
    const ProviderScope(
      child: TijariApp(),
    ),
  );
}
