import 'dart:async';
import 'dart:io';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../core/constants/database_constants.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;
  static bool _isInitializing = false;

  factory DatabaseHelper() => _instance;

  DatabaseHelper._internal();

  Future<Database> get database async {
    if (_database != null) {
      return _database!;
    }

    // منع التهيئة المتعددة
    if (_isInitializing) {
      // انتظار حتى انتهاء التهيئة
      while (_isInitializing) {
        await Future.delayed(const Duration(milliseconds: 10));
      }
      if (_database != null) {
        return _database!;
      }
    }

    _isInitializing = true;
    try {
      _database = await _initDatabase();
      return _database!;
    } finally {
      _isInitializing = false;
    }
  }

  Future<Database> _initDatabase() async {
    try {
      AppUtils.logInfo('بدء تهيئة قاعدة البيانات...');

      // تهيئة sqflite للعمل على سطح المكتب
      if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
        sqfliteFfiInit();
        databaseFactory = databaseFactoryFfi;
        AppUtils.logInfo('تم تهيئة sqflite_ffi للعمل على سطح المكتب');
      }

      final documentsDirectory = await getApplicationDocumentsDirectory();
      final path = join(documentsDirectory.path, AppConstants.databaseName);

      AppUtils.logInfo('Database path: $path');
      // تأكد من وجود المجلد وصلاحيات الكتابة
      final dbFile = File(path);
      final dbDir = dbFile.parent;
      if (!await dbDir.exists()) {
        await dbDir.create(recursive: true);
        AppUtils.logInfo('Created database directory: ${dbDir.path}');
      }

      // التحقق من صلاحيات الكتابة
      try {
        final testFile = File(join(dbDir.path, 'test_write.tmp'));
        await testFile.writeAsString('test');
        await testFile.delete();
        AppUtils.logInfo('تم التحقق من صلاحيات الكتابة بنجاح');
      } catch (e) {
        AppUtils.logError('خطأ في صلاحيات الكتابة', e);
        throw Exception('لا توجد صلاحيات كتابة في مجلد قاعدة البيانات');
      }

      final db = await openDatabase(
        path,
        version: AppConstants.databaseVersion,
        onCreate: _onCreate,
        onUpgrade: _onUpgrade,
        onOpen: _onOpen,
      );

      // اختبار قاعدة البيانات
      await _testDatabase(db);

      AppUtils.logInfo('تم تهيئة قاعدة البيانات بنجاح');
      return db;
    } catch (e) {
      AppUtils.logError('Error initializing database', e);
      rethrow;
    }
  }

  Future<void> _onCreate(Database db, int version) async {
    try {
      AppUtils.logInfo('Creating database tables...');

      // Create all tables
      await _createBranchesTable(db);
      await _createUsersTable(db);
      await _createCategoriesTable(db);
      await _createUnitsTable(db);
      await _createProductsTable(db);
      await _createProductPricesTable(db);
      await _createWarehousesTable(db);
      await _createStocksTable(db);
      await _createStockMovementsTable(db);
      await _createStockAdjustmentsTable(db);
      await _createStockAdjustmentItemsTable(db);
      await _createCustomersTable(db);
      await _createSuppliersTable(db);
      await _createPurchasesTable(db);
      await _createPurchaseItemsTable(db);
      await _createSalesTable(db);
      await _createSaleItemsTable(db);
      await _createCashBoxesTable(db);
      await _createBankAccountsTable(db);
      await _createTransactionsTable(db);
      await _createEmployeesTable(db);
      await _createSalariesTable(db);
      await _createJournalEntriesTable(db);
      await _createNotificationsTable(db);
      await _createDeviceSessionsTable(db);
      await _createAttachmentsTable(db);
      await _createAIPredictionsTable(db);
      await _createAIInsightsTable(db);
      await _createAuditLogTable(db);
      await _createSettingsTable(db);
      await _createSyncLogTable(db);

      // Create Views
      await _createViews(db);
      // Insert default data
      await _insertDefaultData(db);
      await _createDefaultUnits(db);

      AppUtils.logInfo('Database tables created successfully');
    } catch (e) {
      AppUtils.logError('Error creating database tables', e);
      rethrow;
    }
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    try {
      AppUtils.logInfo(
          'Upgrading database from version $oldVersion to $newVersion');

      // Handle specific version upgrades
      if (oldVersion < 4 && newVersion >= 4) {
        // Add approved_by column to stock_adjustments table
        await _addApprovedByColumnToStockAdjustments(db);
      }

      // Add more version-specific migrations here as needed
      // if (oldVersion < 5 && newVersion >= 5) {
      //   await _migrateToVersion5(db);
      // }

      AppUtils.logInfo('Database upgraded successfully');
    } catch (e) {
      AppUtils.logError('Error upgrading database', e);
      // If migration fails, fall back to recreating all tables
      AppUtils.logInfo('Migration failed, recreating all tables...');
      await _dropAllTables(db);
      await _onCreate(db, newVersion);
    }
  }

  Future<void> _onOpen(Database db) async {
    try {
      // Enable foreign key constraints
      await db.execute('PRAGMA foreign_keys = ON');

      AppUtils.logInfo('Database opened successfully');
    } catch (e) {
      AppUtils.logError('Error opening database', e);
      rethrow;
    }
  }

  /// Migration method to add approved_by column to stock_adjustments table
  Future<void> _addApprovedByColumnToStockAdjustments(Database db) async {
    try {
      AppUtils.logInfo(
          'Adding approved_by column to stock_adjustments table...');

      // Check if column already exists
      final tableInfo =
          await db.rawQuery('PRAGMA table_info(stock_adjustments)');
      final hasApprovedByColumn =
          tableInfo.any((column) => column['name'] == 'approved_by');

      if (!hasApprovedByColumn) {
        await db.execute('''
          ALTER TABLE stock_adjustments
          ADD COLUMN approved_by TEXT
        ''');

        // Add foreign key constraint (SQLite doesn't support adding FK constraints to existing tables)
        // The constraint will be enforced in application logic
        AppUtils.logInfo('approved_by column added successfully');
      } else {
        AppUtils.logInfo('approved_by column already exists');
      }
    } catch (e) {
      AppUtils.logError('Error adding approved_by column', e);
      rethrow;
    }
  }

  /// اختبار قاعدة البيانات للتأكد من أنها تعمل بشكل صحيح
  Future<void> _testDatabase(Database db) async {
    try {
      AppUtils.logInfo('بدء اختبار قاعدة البيانات...');

      // اختبار قراءة الجداول
      final tables = await db
          .rawQuery("SELECT name FROM sqlite_master WHERE type='table'");
      AppUtils.logInfo('عدد الجداول: ${tables.length}');

      // اختبار كتابة بسيطة
      const testTable = 'test_write_permissions';
      await db.execute('''
        CREATE TABLE IF NOT EXISTS $testTable (
          id INTEGER PRIMARY KEY,
          test_value TEXT
        )
      ''');

      await db.insert(testTable, {'test_value': 'test'});
      final result = await db.query(testTable);

      if (result.isNotEmpty) {
        AppUtils.logInfo('اختبار الكتابة نجح');
      } else {
        throw Exception('فشل اختبار الكتابة');
      }

      // تنظيف
      await db.execute('DROP TABLE IF EXISTS $testTable');

      AppUtils.logInfo('تم اختبار قاعدة البيانات بنجاح');
    } catch (e) {
      AppUtils.logError('فشل اختبار قاعدة البيانات', e);
      throw Exception('قاعدة البيانات لا تعمل بشكل صحيح: $e');
    }
  }

//
  // Create individual tables using DatabaseConstants
  Future<void> _createBranchesTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${DatabaseConstants.tableBranches} (
        ${DatabaseConstants.columnBranchId} TEXT PRIMARY KEY,
        ${DatabaseConstants.columnBranchName} TEXT NOT NULL,
        ${DatabaseConstants.columnBranchAddress} TEXT,
        ${DatabaseConstants.columnBranchPhone} TEXT,
        ${DatabaseConstants.columnBranchEmail} TEXT,
        ${DatabaseConstants.columnBranchManagerId} TEXT,
        ${DatabaseConstants.columnBranchIsActive} INTEGER DEFAULT 1,
        ${DatabaseConstants.columnBranchCreatedAt} TEXT,
        ${DatabaseConstants.columnBranchUpdatedAt} TEXT,
        ${DatabaseConstants.columnBranchDeletedAt} TEXT,
        ${DatabaseConstants.columnBranchIsSynced} INTEGER DEFAULT 0
      )
    ''');
  }

  Future<void> _createUsersTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${DatabaseConstants.tableUsers} (
        ${DatabaseConstants.columnUserId} TEXT PRIMARY KEY,
        ${DatabaseConstants.columnUserName} TEXT NOT NULL,
        ${DatabaseConstants.columnUserEmail} TEXT UNIQUE NOT NULL,
        ${DatabaseConstants.columnUserPhone} TEXT,
        ${DatabaseConstants.columnUserRole} TEXT NOT NULL,
        ${DatabaseConstants.columnUserPermissions} TEXT NOT NULL,
        ${DatabaseConstants.columnUserIsActive} INTEGER DEFAULT 1,
        ${DatabaseConstants.columnUserProfileImage} TEXT,
        ${DatabaseConstants.columnUserPasswordHash} TEXT,
        ${DatabaseConstants.columnUserCreatedAt} TEXT NOT NULL,
        ${DatabaseConstants.columnUserUpdatedAt} TEXT,
        ${DatabaseConstants.columnUserLastLoginAt} TEXT,
        ${DatabaseConstants.columnUserBranchId} TEXT,
        ${DatabaseConstants.columnUserDeletedAt} TEXT,
        ${DatabaseConstants.columnUserIsSynced} INTEGER DEFAULT 0,
        FOREIGN KEY (${DatabaseConstants.columnUserBranchId}) REFERENCES ${DatabaseConstants.tableBranches}(${DatabaseConstants.columnBranchId})
      )
    ''');
  }

  Future<void> _createCategoriesTable(Database db) async {
    await db.execute('''
    CREATE TABLE ${DatabaseConstants.tableCategories} (
      ${DatabaseConstants.columnCategoryId}          TEXT PRIMARY KEY,
      ${DatabaseConstants.columnCategoryNameAr}      TEXT NOT NULL,
      ${DatabaseConstants.columnCategoryNameEn}      TEXT,
      ${DatabaseConstants.columnCategoryDescription} TEXT,
      ${DatabaseConstants.columnCategoryParentId}    TEXT,
      ${DatabaseConstants.columnCategoryCreatedAt}   TEXT,
      ${DatabaseConstants.columnCategoryUpdatedAt}   TEXT,
      ${DatabaseConstants.columnCategoryDeletedAt}   TEXT,
      ${DatabaseConstants.columnCategoryIsSynced}    INTEGER DEFAULT 0,

      -- الحقول التي أُعيدت بعد الحذف
      ${DatabaseConstants.columnCategoryImageUrl}     TEXT,
      ${DatabaseConstants.columnCategoryIconName}     TEXT,
      ${DatabaseConstants.columnCategoryColor}        TEXT,
      ${DatabaseConstants.columnCategorySortOrder}    INTEGER DEFAULT 0,
      ${DatabaseConstants.columnCategoryIsActive}     INTEGER DEFAULT 1,
      ${DatabaseConstants.columnCategoryTaxRate}      REAL,
      ${DatabaseConstants.columnCategoryDiscountRate} REAL,
      ${DatabaseConstants.columnCategoryTags}         TEXT,
      ${DatabaseConstants.columnCategoryCustomFieds} TEXT,

      FOREIGN KEY (${DatabaseConstants.columnCategoryParentId})
        REFERENCES ${DatabaseConstants.tableCategories}(${DatabaseConstants.columnCategoryId})
        ON DELETE SET NULL
    )
  ''');
  }

  Future<void> _createUnitsTable(Database db) async {
    await db.execute('''
    CREATE TABLE ${DatabaseConstants.tableUnits} (
      ${DatabaseConstants.columnUnitId} TEXT PRIMARY KEY,
      ${DatabaseConstants.columnUnitName} TEXT NOT NULL,
      ${DatabaseConstants.columnUnitSymbol} TEXT,
      ${DatabaseConstants.columnUnitAbbreviation} TEXT,
      ${DatabaseConstants.columnUnitFactor} REAL NOT NULL,
      ${DatabaseConstants.columnUnitBaseUnitId} TEXT,
      ${DatabaseConstants.columnUnitType} TEXT NOT NULL,
      ${DatabaseConstants.columnUnitIsBaseUnit} INTEGER NOT NULL DEFAULT 0,
      ${DatabaseConstants.columnUnitIsActive} INTEGER NOT NULL DEFAULT 1,
      ${DatabaseConstants.columnUnitDescription} TEXT,
      ${DatabaseConstants.columnUnitCreatedAt} TEXT,
      ${DatabaseConstants.columnUnitUpdatedAt} TEXT,
      ${DatabaseConstants.columnUnitDeletedAt} TEXT,
      ${DatabaseConstants.columnUnitIsSynced} INTEGER DEFAULT 0,
      FOREIGN KEY (${DatabaseConstants.columnUnitBaseUnitId}) 
        REFERENCES ${DatabaseConstants.tableUnits}(${DatabaseConstants.columnUnitId})
        ON DELETE SET NULL
    )
  ''');
  }

  Future<void> _createProductsTable(Database db) async {
    await db.execute('''
    CREATE TABLE ${DatabaseConstants.tableProducts} (
      ${DatabaseConstants.columnProductId}                   TEXT PRIMARY KEY,
      ${DatabaseConstants.columnProductNameAr}               TEXT NOT NULL,
      ${DatabaseConstants.columnProductNameEn}               TEXT,
      ${DatabaseConstants.columnProductBarcode}              TEXT UNIQUE,
      ${DatabaseConstants.columnProductQrCode}               TEXT,
      ${DatabaseConstants.columnProductDescription}          TEXT,
      ${DatabaseConstants.columnProductCategoryId}           TEXT,
      ${DatabaseConstants.columnProductBaseUnitId}           TEXT,
      ${DatabaseConstants.columnProductCostPrice}            REAL NOT NULL,
      ${DatabaseConstants.columnProductSellingPrice}         REAL NOT NULL,
      ${DatabaseConstants.columnProductMinStock}             REAL DEFAULT 0,
      ${DatabaseConstants.columnProductMaxStock}             REAL DEFAULT 0,
      ${DatabaseConstants.columnProductReorderPoint}         REAL DEFAULT 0,
      ${DatabaseConstants.columnProductTrackStock}           INTEGER DEFAULT 1,
      ${DatabaseConstants.columnProductIsActive}             INTEGER DEFAULT 1,
      ${DatabaseConstants.columnProductAllowNegativeStock}   INTEGER DEFAULT 0,
      ${DatabaseConstants.columnProductHasExpiryDate}        INTEGER DEFAULT 0,
      ${DatabaseConstants.columnProductExpiryDate}           TEXT,
      ${DatabaseConstants.columnProductWeight}               REAL,
      ${DatabaseConstants.columnProductVolume}               REAL,
      ${DatabaseConstants.columnProductSku}                  TEXT,
      ${DatabaseConstants.columnProductManufacturerCode}     TEXT,
      ${DatabaseConstants.columnProductSupplierCode}         TEXT,
      ${DatabaseConstants.columnProductTags}                 TEXT,
      ${DatabaseConstants.columnProductNotes}                TEXT,
      ${DatabaseConstants.columnProductImageUrl}             TEXT,
      ${DatabaseConstants.columnProductCreatedAt}            TEXT,
      ${DatabaseConstants.columnProductUpdatedAt}            TEXT,
      ${DatabaseConstants.columnProductDeletedAt}            TEXT,
      ${DatabaseConstants.columnProductIsSynced}             INTEGER DEFAULT 0,

      FOREIGN KEY (${DatabaseConstants.columnProductCategoryId})
        REFERENCES ${DatabaseConstants.tableCategories}(${DatabaseConstants.columnCategoryId})
        ON DELETE SET NULL,
      FOREIGN KEY (${DatabaseConstants.columnProductBaseUnitId})
        REFERENCES ${DatabaseConstants.tableUnits}(${DatabaseConstants.columnUnitId})
    )
  ''');
  }

  Future<void> _createProductPricesTable(Database db) async {
    await db.execute('''
    CREATE TABLE ${DatabaseConstants.tableProductPrices} (
      ${DatabaseConstants.columnProductPriceId} TEXT PRIMARY KEY,
      ${DatabaseConstants.columnProductPriceProductId} TEXT NOT NULL,
      ${DatabaseConstants.columnProductPriceUnitId} TEXT NOT NULL,
      ${DatabaseConstants.columnProductPricePurchasePrice} REAL NOT NULL,
      ${DatabaseConstants.columnProductPriceSalePrice} REAL NOT NULL,
      ${DatabaseConstants.columnProductPriceCreatedAt} TEXT,
      ${DatabaseConstants.columnProductPriceUpdatedAt} TEXT,
      ${DatabaseConstants.columnProductPriceDeletedAt} TEXT,
      ${DatabaseConstants.columnProductPriceIsSynced} INTEGER DEFAULT 0,
      FOREIGN KEY (${DatabaseConstants.columnProductPriceProductId}) REFERENCES ${DatabaseConstants.tableProducts}(${DatabaseConstants.columnProductId}),
      FOREIGN KEY (${DatabaseConstants.columnProductPriceUnitId}) REFERENCES ${DatabaseConstants.tableUnits}(${DatabaseConstants.columnUnitId})
    )
  ''');
  }

  Future<void> _createWarehousesTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${DatabaseConstants.tableWarehouses} (
        ${DatabaseConstants.columnWarehouseId} TEXT PRIMARY KEY,
        ${DatabaseConstants.columnWarehouseName} TEXT NOT NULL,
        ${DatabaseConstants.columnWarehouseLocation} TEXT,
        address TEXT,
        ${DatabaseConstants.columnWarehouseManagerId} TEXT,
        phone TEXT,
        email TEXT,
        ${DatabaseConstants.columnWarehouseIsActive} INTEGER DEFAULT 1,
        is_default INTEGER DEFAULT 0,
        description TEXT,
        notes TEXT,
        capacity REAL,
        warehouse_type TEXT DEFAULT 'main',
        ${DatabaseConstants.columnWarehouseCreatedAt} TEXT NOT NULL,
        ${DatabaseConstants.columnWarehouseUpdatedAt} TEXT,
        ${DatabaseConstants.columnWarehouseDeletedAt} TEXT,
        ${DatabaseConstants.columnWarehouseBranchId} TEXT,
        ${DatabaseConstants.columnWarehouseIsSynced} INTEGER DEFAULT 0,
        FOREIGN KEY (${DatabaseConstants.columnWarehouseManagerId}) REFERENCES ${DatabaseConstants.tableUsers}(${DatabaseConstants.columnUserId}),
        FOREIGN KEY (${DatabaseConstants.columnWarehouseBranchId}) REFERENCES ${DatabaseConstants.tableBranches}(${DatabaseConstants.columnBranchId})
      )
    ''');
  }

  Future<void> _createStocksTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${DatabaseConstants.tableStocks} (
        ${DatabaseConstants.columnStockId} TEXT PRIMARY KEY,
        ${DatabaseConstants.columnStockProductId} TEXT NOT NULL,
        ${DatabaseConstants.columnStockWarehouseId} TEXT NOT NULL,
        ${DatabaseConstants.columnStockQuantity} REAL NOT NULL DEFAULT 0,
        reserved_quantity REAL DEFAULT 0,
        available_quantity REAL DEFAULT 0,
        min_stock REAL DEFAULT 0,
        max_stock REAL DEFAULT 0,
        reorder_point REAL DEFAULT 0,
        average_cost REAL DEFAULT 0,
        last_movement_date TEXT,
        last_count_date TEXT,
        location TEXT,
        batch_number TEXT,
        expiry_date TEXT,
        ${DatabaseConstants.columnStockCreatedAt} TEXT,
        ${DatabaseConstants.columnStockUpdatedAt} TEXT,
        ${DatabaseConstants.columnStockDeletedAt} TEXT,
        ${DatabaseConstants.columnStockIsSynced} INTEGER DEFAULT 0,
        FOREIGN KEY (${DatabaseConstants.columnStockProductId}) REFERENCES ${DatabaseConstants.tableProducts}(${DatabaseConstants.columnProductId}),
        FOREIGN KEY (${DatabaseConstants.columnStockWarehouseId}) REFERENCES ${DatabaseConstants.tableWarehouses}(${DatabaseConstants.columnWarehouseId})
      )
    ''');
  }

  Future<void> _createStockMovementsTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${DatabaseConstants.tableStockMovements} (
        ${DatabaseConstants.columnStockMovementId} TEXT PRIMARY KEY,
        ${DatabaseConstants.columnStockMovementProductId} TEXT NOT NULL,
        ${DatabaseConstants.columnStockMovementWarehouseId} TEXT NOT NULL,
        ${DatabaseConstants.columnStockMovementType} TEXT NOT NULL,
        ${DatabaseConstants.columnStockMovementQuantity} REAL NOT NULL,
        ${DatabaseConstants.columnStockMovementQty} REAL,
        ${DatabaseConstants.columnStockMovementUnitCost} REAL NOT NULL,
        ${DatabaseConstants.columnStockMovementTotalValue} REAL NOT NULL,
        ${DatabaseConstants.columnStockMovementUnitId} TEXT NOT NULL,
        ${DatabaseConstants.columnStockMovementReferenceType} TEXT,
        ${DatabaseConstants.columnStockMovementReferenceId} TEXT,
        reference_number TEXT,
        from_warehouse_id TEXT,
        to_warehouse_id TEXT,
        batch_number TEXT,
        expiry_date TEXT,
        ${DatabaseConstants.columnStockMovementNotes} TEXT,
        reason TEXT,
        ${DatabaseConstants.columnStockMovementCreatedBy} TEXT NOT NULL,
        ${DatabaseConstants.columnStockMovementCreatedAt} TEXT,
        ${DatabaseConstants.columnStockMovementUpdatedAt} TEXT,
        ${DatabaseConstants.columnStockMovementDeletedAt} TEXT,
        ${DatabaseConstants.columnStockMovementBranchId} TEXT,
        ${DatabaseConstants.columnStockMovementIsSynced} INTEGER DEFAULT 0,
        FOREIGN KEY (${DatabaseConstants.columnStockMovementProductId}) REFERENCES ${DatabaseConstants.tableProducts}(${DatabaseConstants.columnProductId}),
        FOREIGN KEY (${DatabaseConstants.columnStockMovementWarehouseId}) REFERENCES ${DatabaseConstants.tableWarehouses}(${DatabaseConstants.columnWarehouseId}),
        FOREIGN KEY (${DatabaseConstants.columnStockMovementUnitId}) REFERENCES ${DatabaseConstants.tableUnits}(${DatabaseConstants.columnUnitId}),
        FOREIGN KEY (${DatabaseConstants.columnStockMovementCreatedBy}) REFERENCES ${DatabaseConstants.tableUsers}(${DatabaseConstants.columnUserId}),
        FOREIGN KEY (${DatabaseConstants.columnStockMovementBranchId}) REFERENCES ${DatabaseConstants.tableBranches}(${DatabaseConstants.columnBranchId}),
        FOREIGN KEY (from_warehouse_id) REFERENCES ${DatabaseConstants.tableWarehouses}(${DatabaseConstants.columnWarehouseId}),
        FOREIGN KEY (to_warehouse_id) REFERENCES ${DatabaseConstants.tableWarehouses}(${DatabaseConstants.columnWarehouseId})
      )
    ''');
  }

  // Stock Adjustments tables

  Future<void> _createStockAdjustmentsTable(Database db) async {
    await db.execute('''
    CREATE TABLE ${DatabaseConstants.tableStockAdjustments} (
      ${DatabaseConstants.columnStockAdjustmentId} TEXT PRIMARY KEY,
      ${DatabaseConstants.columnStockAdjustmentReferenceNumber} TEXT UNIQUE NOT NULL,
      ${DatabaseConstants.columnStockAdjustmentDate} TEXT NOT NULL,
      ${DatabaseConstants.columnStockAdjustmentWarehouseId} TEXT NOT NULL,
      ${DatabaseConstants.columnStockAdjustmentReason} TEXT NOT NULL,
      ${DatabaseConstants.columnStockAdjustmentNotes} TEXT,
      ${DatabaseConstants.columnStockAdjustmentStatus} TEXT DEFAULT 'draft',
      ${DatabaseConstants.columnStockAdjustmentTotalValue} REAL DEFAULT 0.0,
      ${DatabaseConstants.columnStockAdjustmentCreatedBy} TEXT NOT NULL,
      ${DatabaseConstants.columnStockAdjustmentApprovedBy} TEXT,
      ${DatabaseConstants.columnStockAdjustmentCreatedAt} TEXT NOT NULL,
      ${DatabaseConstants.columnStockAdjustmentUpdatedAt} TEXT,
      ${DatabaseConstants.columnStockAdjustmentDeletedAt} TEXT,
      ${DatabaseConstants.columnStockAdjustmentBranchId} TEXT,
      ${DatabaseConstants.columnStockAdjustmentIsSynced} INTEGER DEFAULT 0,
      FOREIGN KEY (${DatabaseConstants.columnStockAdjustmentWarehouseId}) REFERENCES ${DatabaseConstants.tableWarehouses}(${DatabaseConstants.columnWarehouseId}),
      FOREIGN KEY (${DatabaseConstants.columnStockAdjustmentCreatedBy}) REFERENCES ${DatabaseConstants.tableUsers}(${DatabaseConstants.columnUserId}),
      FOREIGN KEY (${DatabaseConstants.columnStockAdjustmentApprovedBy}) REFERENCES ${DatabaseConstants.tableUsers}(${DatabaseConstants.columnUserId}),
      FOREIGN KEY (${DatabaseConstants.columnStockAdjustmentBranchId}) REFERENCES ${DatabaseConstants.tableBranches}(${DatabaseConstants.columnBranchId})
    )
  ''');
  }

  Future<void> _createStockAdjustmentItemsTable(Database db) async {
    await db.execute('''
    CREATE TABLE ${DatabaseConstants.tableStockAdjustmentItems} (
      ${DatabaseConstants.columnStockAdjustmentItemId} TEXT PRIMARY KEY,
      ${DatabaseConstants.columnStockAdjustmentItemAdjustmentId} TEXT NOT NULL,
      ${DatabaseConstants.columnStockAdjustmentItemProductId} TEXT NOT NULL,
      ${DatabaseConstants.columnStockAdjustmentItemCurrentQuantity} REAL NOT NULL,
      ${DatabaseConstants.columnStockAdjustmentItemAdjustmentQuantity} REAL NOT NULL,
      ${DatabaseConstants.columnStockAdjustmentItemNewQuantity} REAL NOT NULL,
      ${DatabaseConstants.columnStockAdjustmentItemType} TEXT NOT NULL,
      ${DatabaseConstants.columnStockAdjustmentItemUnitCost} REAL NOT NULL,
      ${DatabaseConstants.columnStockAdjustmentItemTotalValue} REAL NOT NULL,
      ${DatabaseConstants.columnStockAdjustmentItemNotes} TEXT,
      ${DatabaseConstants.columnStockAdjustmentItemCreatedAt} TEXT,
      ${DatabaseConstants.columnStockAdjustmentItemUpdatedAt} TEXT,
      ${DatabaseConstants.columnStockAdjustmentItemDeletedAt} TEXT,
      ${DatabaseConstants.columnStockAdjustmentItemIsSynced} INTEGER DEFAULT 0,
      FOREIGN KEY (${DatabaseConstants.columnStockAdjustmentItemAdjustmentId}) REFERENCES ${DatabaseConstants.tableStockAdjustments}(${DatabaseConstants.columnStockAdjustmentId}),
      FOREIGN KEY (${DatabaseConstants.columnStockAdjustmentItemProductId}) REFERENCES ${DatabaseConstants.tableProducts}(${DatabaseConstants.columnProductId})
    )
  ''');
  }

  Future<void> _createCustomersTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${DatabaseConstants.tableCustomers} (
        ${DatabaseConstants.columnCustomerId} TEXT PRIMARY KEY,
        ${DatabaseConstants.columnCustomerName} TEXT NOT NULL,
        ${DatabaseConstants.columnCustomerPhone} TEXT,
        ${DatabaseConstants.columnCustomerEmail} TEXT,
        ${DatabaseConstants.columnCustomerAddress} TEXT,
        ${DatabaseConstants.columnCustomerBalance} REAL DEFAULT 0,
        ${DatabaseConstants.columnCustomerNotes} TEXT,
        ${DatabaseConstants.columnCustomerCreatedAt} TEXT,
        ${DatabaseConstants.columnCustomerUpdatedAt} TEXT,
        ${DatabaseConstants.columnCustomerDeletedAt} TEXT,
        ${DatabaseConstants.columnCustomerIsSynced} INTEGER DEFAULT 0
      )
    ''');
  }

  Future<void> _createSuppliersTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${DatabaseConstants.tableSuppliers} (
        ${DatabaseConstants.columnSupplierId} TEXT PRIMARY KEY,
        ${DatabaseConstants.columnSupplierName} TEXT NOT NULL,
        ${DatabaseConstants.columnSupplierPhone} TEXT,
        ${DatabaseConstants.columnSupplierEmail} TEXT,
        ${DatabaseConstants.columnSupplierAddress} TEXT,
        ${DatabaseConstants.columnSupplierBalance} REAL DEFAULT 0,
        ${DatabaseConstants.columnSupplierNotes} TEXT,
        ${DatabaseConstants.columnSupplierCreatedAt} TEXT,
        ${DatabaseConstants.columnSupplierUpdatedAt} TEXT,
        ${DatabaseConstants.columnSupplierDeletedAt} TEXT,
        ${DatabaseConstants.columnSupplierIsSynced} INTEGER DEFAULT 0
      )
    ''');
  }

  Future<void> _createPurchasesTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${DatabaseConstants.tablePurchases} (
        ${DatabaseConstants.columnPurchaseId} TEXT PRIMARY KEY,
        ${DatabaseConstants.columnPurchaseSupplierId} TEXT,
        ${DatabaseConstants.columnPurchaseInvoiceNo} TEXT,
        ${DatabaseConstants.columnPurchaseBranchId} TEXT,
        ${DatabaseConstants.columnPurchasePurchaseDate} TEXT,
        ${DatabaseConstants.columnPurchaseTotalAmount} REAL,
        ${DatabaseConstants.columnPurchasePaidAmount} REAL,
        ${DatabaseConstants.columnPurchaseDueAmount} REAL,
        ${DatabaseConstants.columnPurchaseNotes} TEXT,
        ${DatabaseConstants.columnPurchaseCreatedAt} TEXT,
        ${DatabaseConstants.columnPurchaseUpdatedAt} TEXT,
        ${DatabaseConstants.columnPurchaseDeletedAt} TEXT,
        ${DatabaseConstants.columnPurchaseIsSynced} INTEGER DEFAULT 0,
        FOREIGN KEY (${DatabaseConstants.columnPurchaseSupplierId}) REFERENCES ${DatabaseConstants.tableSuppliers}(${DatabaseConstants.columnSupplierId}),
        FOREIGN KEY (${DatabaseConstants.columnPurchaseBranchId}) REFERENCES ${DatabaseConstants.tableBranches}(${DatabaseConstants.columnBranchId})
      )
    ''');
  }

  Future<void> _createPurchaseItemsTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${DatabaseConstants.tablePurchaseItems} (
        ${DatabaseConstants.columnPurchaseItemId} TEXT PRIMARY KEY,
        ${DatabaseConstants.columnPurchaseItemPurchaseId} TEXT,
        ${DatabaseConstants.columnPurchaseItemProductId} TEXT,
        ${DatabaseConstants.columnPurchaseItemUnitId} TEXT,
        ${DatabaseConstants.columnPurchaseItemQty} REAL,
        ${DatabaseConstants.columnPurchaseItemUnitPrice} REAL,
        ${DatabaseConstants.columnPurchaseItemTotalPrice} REAL,
        ${DatabaseConstants.columnPurchaseItemCreatedAt} TEXT,
        ${DatabaseConstants.columnPurchaseItemUpdatedAt} TEXT,
        ${DatabaseConstants.columnPurchaseItemDeletedAt} TEXT,
        ${DatabaseConstants.columnPurchaseItemIsSynced} INTEGER DEFAULT 0,
        FOREIGN KEY (${DatabaseConstants.columnPurchaseItemPurchaseId}) REFERENCES ${DatabaseConstants.tablePurchases}(${DatabaseConstants.columnPurchaseId}),
        FOREIGN KEY (${DatabaseConstants.columnPurchaseItemProductId}) REFERENCES ${DatabaseConstants.tableProducts}(${DatabaseConstants.columnProductId}),
        FOREIGN KEY (${DatabaseConstants.columnPurchaseItemUnitId}) REFERENCES ${DatabaseConstants.tableUnits}(${DatabaseConstants.columnUnitId})
      )
    ''');
  }

  Future<void> _createSalesTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${DatabaseConstants.tableSales} (
        ${DatabaseConstants.columnSaleId} TEXT PRIMARY KEY,
        ${DatabaseConstants.columnSaleCustomerId} TEXT,
        ${DatabaseConstants.columnSaleInvoiceNo} TEXT,
        ${DatabaseConstants.columnSaleBranchId} TEXT,
        ${DatabaseConstants.columnSaleSaleDate} TEXT,
        ${DatabaseConstants.columnSaleTotalAmount} REAL,
        ${DatabaseConstants.columnSalePaidAmount} REAL,
        ${DatabaseConstants.columnSaleDueAmount} REAL,
        ${DatabaseConstants.columnSaleNotes} TEXT,
        ${DatabaseConstants.columnSaleCreatedAt} TEXT,
        ${DatabaseConstants.columnSaleUpdatedAt} TEXT,
        ${DatabaseConstants.columnSaleDeletedAt} TEXT,
        ${DatabaseConstants.columnSaleIsSynced} INTEGER DEFAULT 0,
        FOREIGN KEY (${DatabaseConstants.columnSaleCustomerId}) REFERENCES ${DatabaseConstants.tableCustomers}(${DatabaseConstants.columnCustomerId}),
        FOREIGN KEY (${DatabaseConstants.columnSaleBranchId}) REFERENCES ${DatabaseConstants.tableBranches}(${DatabaseConstants.columnBranchId})
      )
    ''');
  }

  Future<void> _createSaleItemsTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${DatabaseConstants.tableSaleItems} (
        ${DatabaseConstants.columnSaleItemId} TEXT PRIMARY KEY,
        ${DatabaseConstants.columnSaleItemSaleId} TEXT,
        ${DatabaseConstants.columnSaleItemProductId} TEXT,
        ${DatabaseConstants.columnSaleItemUnitId} TEXT,
        ${DatabaseConstants.columnSaleItemQty} REAL,
        ${DatabaseConstants.columnSaleItemUnitPrice} REAL,
        ${DatabaseConstants.columnSaleItemTotalPrice} REAL,
        ${DatabaseConstants.columnSaleItemCreatedAt} TEXT,
        ${DatabaseConstants.columnSaleItemUpdatedAt} TEXT,
        ${DatabaseConstants.columnSaleItemDeletedAt} TEXT,
        ${DatabaseConstants.columnSaleItemIsSynced} INTEGER DEFAULT 0,
        FOREIGN KEY (${DatabaseConstants.columnSaleItemSaleId}) REFERENCES ${DatabaseConstants.tableSales}(${DatabaseConstants.columnSaleId}),
        FOREIGN KEY (${DatabaseConstants.columnSaleItemProductId}) REFERENCES ${DatabaseConstants.tableProducts}(${DatabaseConstants.columnProductId}),
        FOREIGN KEY (${DatabaseConstants.columnSaleItemUnitId}) REFERENCES ${DatabaseConstants.tableUnits}(${DatabaseConstants.columnUnitId})
      )
    ''');
  }

  Future<void> _createCashBoxesTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${DatabaseConstants.tableCashBoxes} (
        ${DatabaseConstants.columnCashBoxId} TEXT PRIMARY KEY,
        ${DatabaseConstants.columnCashBoxName} TEXT NOT NULL,
        ${DatabaseConstants.columnCashBoxBalance} REAL DEFAULT 0,
        ${DatabaseConstants.columnCashBoxIsActive} INTEGER DEFAULT 1,
        ${DatabaseConstants.columnCashBoxCreatedAt} TEXT,
        ${DatabaseConstants.columnCashBoxUpdatedAt} TEXT,
        ${DatabaseConstants.columnCashBoxDeletedAt} TEXT,
        ${DatabaseConstants.columnCashBoxBranchId} TEXT,
        ${DatabaseConstants.columnCashBoxIsSynced} INTEGER DEFAULT 0,
        FOREIGN KEY (${DatabaseConstants.columnCashBoxBranchId}) REFERENCES ${DatabaseConstants.tableBranches}(${DatabaseConstants.columnBranchId})
      )
    ''');
  }

  Future<void> _createBankAccountsTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${DatabaseConstants.tableBankAccounts} (
        ${DatabaseConstants.columnBankAccountId} TEXT PRIMARY KEY,
        ${DatabaseConstants.columnBankAccountBankName} TEXT NOT NULL,
        ${DatabaseConstants.columnBankAccountAccountNumber} TEXT NOT NULL,
        ${DatabaseConstants.columnBankAccountAccountName} TEXT NOT NULL,
        ${DatabaseConstants.columnBankAccountIban} TEXT,
        ${DatabaseConstants.columnBankAccountBalance} REAL DEFAULT 0.0,
        ${DatabaseConstants.columnBankAccountIsActive} INTEGER DEFAULT 1,
        ${DatabaseConstants.columnBankAccountCreatedAt} TEXT NOT NULL,
        ${DatabaseConstants.columnBankAccountUpdatedAt} TEXT,
        ${DatabaseConstants.columnBankAccountDeletedAt} TEXT,
        ${DatabaseConstants.columnBankAccountBranchId} TEXT,
        ${DatabaseConstants.columnBankAccountIsSynced} INTEGER DEFAULT 0,
        FOREIGN KEY (${DatabaseConstants.columnBankAccountBranchId}) REFERENCES ${DatabaseConstants.tableBranches}(${DatabaseConstants.columnBranchId})
      )
    ''');
  }

  Future<void> _createTransactionsTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${DatabaseConstants.tableTransactions} (
        ${DatabaseConstants.columnTransactionId} TEXT PRIMARY KEY,
        ${DatabaseConstants.columnTransactionType} TEXT,
        ${DatabaseConstants.columnTransactionSubType} TEXT,
        ${DatabaseConstants.columnTransactionAmount} REAL,
        ${DatabaseConstants.columnTransactionCustomerId} TEXT,
        ${DatabaseConstants.columnTransactionSupplierId} TEXT,
        ${DatabaseConstants.columnTransactionCashBoxId} TEXT,
        ${DatabaseConstants.columnTransactionBankAccountId} TEXT,
        ${DatabaseConstants.columnTransactionTransactionDate} TEXT,
        ${DatabaseConstants.columnTransactionNotes} TEXT,
        ${DatabaseConstants.columnTransactionCreatedAt} TEXT,
        ${DatabaseConstants.columnTransactionUpdatedAt} TEXT,
        ${DatabaseConstants.columnTransactionDeletedAt} TEXT,
        ${DatabaseConstants.columnTransactionIsSynced} INTEGER DEFAULT 0,
        FOREIGN KEY (${DatabaseConstants.columnTransactionCashBoxId}) REFERENCES ${DatabaseConstants.tableCashBoxes}(${DatabaseConstants.columnCashBoxId}),
        FOREIGN KEY (${DatabaseConstants.columnTransactionBankAccountId}) REFERENCES ${DatabaseConstants.tableBankAccounts}(${DatabaseConstants.columnBankAccountId})
      )
    ''');
  }

  Future<void> _createEmployeesTable(Database db) async {
    await db.execute('''
    CREATE TABLE ${DatabaseConstants.tableEmployees} (
      ${DatabaseConstants.columnEmployeeId} TEXT PRIMARY KEY,
      ${DatabaseConstants.columnEmployeeName} TEXT NOT NULL,
      ${DatabaseConstants.columnEmployeePhone} TEXT,
      ${DatabaseConstants.columnEmployeeJobTitle} TEXT,
      ${DatabaseConstants.columnEmployeeSalaryFixed} REAL,
      ${DatabaseConstants.columnEmployeeNotes} TEXT,
      ${DatabaseConstants.columnEmployeeCreatedAt} TEXT,
      ${DatabaseConstants.columnEmployeeUpdatedAt} TEXT,
      ${DatabaseConstants.columnEmployeeDeletedAt} TEXT,
      ${DatabaseConstants.columnEmployeeIsSynced} INTEGER DEFAULT 0
    )
  ''');
  }

  Future<void> _createSalariesTable(Database db) async {
    await db.execute('''
    CREATE TABLE ${DatabaseConstants.tableSalaries} (
      ${DatabaseConstants.columnSalaryId} TEXT PRIMARY KEY,
      ${DatabaseConstants.columnSalaryEmployeeId} TEXT,
      ${DatabaseConstants.columnSalaryMonth} TEXT,
      ${DatabaseConstants.columnSalaryBaseSalary} REAL,
      ${DatabaseConstants.columnSalaryBonus} REAL,
      ${DatabaseConstants.columnSalaryAdvance} REAL,
      ${DatabaseConstants.columnSalaryDeductions} REAL,
      ${DatabaseConstants.columnSalaryNetSalary} REAL,
      ${DatabaseConstants.columnSalaryPaid} REAL,
      ${DatabaseConstants.columnSalaryDate} TEXT,
      ${DatabaseConstants.columnSalaryCreatedAt} TEXT,
      ${DatabaseConstants.columnSalaryUpdatedAt} TEXT,
      ${DatabaseConstants.columnSalaryDeletedAt} TEXT,
      ${DatabaseConstants.columnSalaryIsSynced} INTEGER DEFAULT 0,
      FOREIGN KEY (${DatabaseConstants.columnSalaryEmployeeId}) REFERENCES ${DatabaseConstants.tableEmployees}(${DatabaseConstants.columnEmployeeId})
    )
  ''');
  }

  Future<void> _createJournalEntriesTable(Database db) async {
    await db.execute('''
    CREATE TABLE ${DatabaseConstants.tableJournalEntries} (
      ${DatabaseConstants.columnJournalEntryId} TEXT PRIMARY KEY,
      ${DatabaseConstants.columnJournalEntryDebitAccount} TEXT,
      ${DatabaseConstants.columnJournalEntryCreditAccount} TEXT,
      ${DatabaseConstants.columnJournalEntryAmount} REAL,
      ${DatabaseConstants.columnJournalEntryDate} TEXT,
      ${DatabaseConstants.columnJournalEntryNotes} TEXT,
      ${DatabaseConstants.columnJournalEntryCreatedAt} TEXT,
      ${DatabaseConstants.columnJournalEntryUpdatedAt} TEXT,
      ${DatabaseConstants.columnJournalEntryDeletedAt} TEXT,
      ${DatabaseConstants.columnJournalEntryIsSynced} INTEGER DEFAULT 0
    )
  ''');
  }

  Future<void> _createNotificationsTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${DatabaseConstants.tableNotifications} (
        ${DatabaseConstants.columnNotificationId} TEXT PRIMARY KEY,
        ${DatabaseConstants.columnNotificationUserId} TEXT,
        ${DatabaseConstants.columnNotificationTitle} TEXT NOT NULL,
        ${DatabaseConstants.columnNotificationBody} TEXT NOT NULL,
        ${DatabaseConstants.columnNotificationType} TEXT DEFAULT 'info',
        ${DatabaseConstants.columnNotificationIsRead} INTEGER DEFAULT 0,
        ${DatabaseConstants.columnNotificationMetadata} TEXT,
        ${DatabaseConstants.columnNotificationCreatedAt} TEXT NOT NULL,
        ${DatabaseConstants.columnNotificationReadAt} TEXT,
        ${DatabaseConstants.columnNotificationDeletedAt} TEXT,
        ${DatabaseConstants.columnNotificationIsSynced} INTEGER DEFAULT 0,
        FOREIGN KEY (${DatabaseConstants.columnNotificationUserId}) REFERENCES ${DatabaseConstants.tableUsers}(${DatabaseConstants.columnUserId})
      )
    ''');
  }

  Future<void> _createDeviceSessionsTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${DatabaseConstants.tableDeviceSessions} (
        ${DatabaseConstants.columnDeviceSessionId} TEXT PRIMARY KEY,
        ${DatabaseConstants.columnDeviceSessionUserId} TEXT NOT NULL,
        ${DatabaseConstants.columnDeviceSessionDeviceName} TEXT,
        ${DatabaseConstants.columnDeviceSessionDeviceOs} TEXT,
        ${DatabaseConstants.columnDeviceSessionAppVersion} TEXT,
        ${DatabaseConstants.columnDeviceSessionIpAddress} TEXT,
        ${DatabaseConstants.columnDeviceSessionToken} TEXT UNIQUE,
        ${DatabaseConstants.columnDeviceSessionIsActive} INTEGER DEFAULT 1,
        ${DatabaseConstants.columnDeviceSessionLastLoginAt} TEXT,
        ${DatabaseConstants.columnDeviceSessionCreatedAt} TEXT NOT NULL,
        ${DatabaseConstants.columnDeviceSessionUpdatedAt} TEXT,
        ${DatabaseConstants.columnDeviceSessionDeletedAt} TEXT,
        ${DatabaseConstants.columnDeviceSessionIsSynced} INTEGER DEFAULT 0,
        FOREIGN KEY (${DatabaseConstants.columnDeviceSessionUserId}) REFERENCES ${DatabaseConstants.tableUsers}(${DatabaseConstants.columnUserId})
      )
    ''');
  }

  Future<void> _createAttachmentsTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${DatabaseConstants.tableAttachments} (
        ${DatabaseConstants.columnAttachmentId} TEXT PRIMARY KEY,
        ${DatabaseConstants.columnAttachmentReferenceType} TEXT NOT NULL,
        ${DatabaseConstants.columnAttachmentReferenceId} TEXT NOT NULL,
        ${DatabaseConstants.columnAttachmentFileName} TEXT NOT NULL,
        ${DatabaseConstants.columnAttachmentFilePath} TEXT NOT NULL,
        ${DatabaseConstants.columnAttachmentMimeType} TEXT,
        ${DatabaseConstants.columnAttachmentSize} INTEGER,
        ${DatabaseConstants.columnAttachmentUploadedBy} TEXT,
        ${DatabaseConstants.columnAttachmentCreatedAt} TEXT NOT NULL,
        ${DatabaseConstants.columnAttachmentUpdatedAt} TEXT,
        ${DatabaseConstants.columnAttachmentDeletedAt} TEXT,
        ${DatabaseConstants.columnAttachmentIsSynced} INTEGER DEFAULT 0,
        FOREIGN KEY (${DatabaseConstants.columnAttachmentUploadedBy}) REFERENCES ${DatabaseConstants.tableUsers}(${DatabaseConstants.columnUserId})
      )
    ''');
  }

  Future<void> _createAIPredictionsTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${DatabaseConstants.tableAiPredictions} (
        ${DatabaseConstants.columnAiPredictionId} TEXT PRIMARY KEY,
        ${DatabaseConstants.columnAiPredictionType} TEXT NOT NULL,
        ${DatabaseConstants.columnAiPredictionPeriod} TEXT NOT NULL,
        ${DatabaseConstants.columnAiPredictionPredictionDate} TEXT NOT NULL,
        ${DatabaseConstants.columnAiPredictionTargetDate} TEXT NOT NULL,
        ${DatabaseConstants.columnAiPredictionInputData} TEXT NOT NULL,
        ${DatabaseConstants.columnAiPredictionPredictions} TEXT NOT NULL,
        ${DatabaseConstants.columnAiPredictionConfidence} REAL NOT NULL,
        ${DatabaseConstants.columnAiPredictionAlgorithm} TEXT NOT NULL,
        ${DatabaseConstants.columnAiPredictionMetadata} TEXT NOT NULL,
        ${DatabaseConstants.columnAiPredictionCreatedAt} TEXT NOT NULL
      )
    ''');
  }

  Future<void> _createAIInsightsTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${DatabaseConstants.tableAiInsights} (
        ${DatabaseConstants.columnAiInsightId} TEXT PRIMARY KEY,
        ${DatabaseConstants.columnAiInsightType} TEXT NOT NULL,
        ${DatabaseConstants.columnAiInsightTitle} TEXT NOT NULL,
        ${DatabaseConstants.columnAiInsightDescription} TEXT NOT NULL,
        ${DatabaseConstants.columnAiInsightData} TEXT NOT NULL,
        ${DatabaseConstants.columnAiInsightPriority} TEXT NOT NULL,
        ${DatabaseConstants.columnAiInsightStatus} TEXT NOT NULL,
        ${DatabaseConstants.columnAiInsightCreatedAt} TEXT NOT NULL,
        ${DatabaseConstants.columnAiInsightUpdatedAt} TEXT
      )
    ''');
  }

  Future<void> _createAuditLogTable(Database db) async {
    await db.execute('''
    CREATE TABLE ${DatabaseConstants.tableAuditLog} (
      ${DatabaseConstants.columnAuditLogId} TEXT PRIMARY KEY,
      ${DatabaseConstants.columnAuditLogUserId} TEXT NOT NULL,
      ${DatabaseConstants.columnAuditLogAction} TEXT NOT NULL,
      ${DatabaseConstants.columnAuditLogTableName} TEXT NOT NULL,
      ${DatabaseConstants.columnAuditLogRecordId} TEXT NOT NULL,
      ${DatabaseConstants.columnAuditLogOldValues} TEXT,
      ${DatabaseConstants.columnAuditLogNewValues} TEXT,
      ${DatabaseConstants.columnAuditLogIpAddress} TEXT,
      ${DatabaseConstants.columnAuditLogUserAgent} TEXT,
      ${DatabaseConstants.columnAuditLogCreatedAt} TEXT NOT NULL,
      ${DatabaseConstants.columnAuditLogIsSynced} INTEGER DEFAULT 0,
      FOREIGN KEY (${DatabaseConstants.columnAuditLogUserId}) REFERENCES ${DatabaseConstants.tableUsers}(${DatabaseConstants.columnUserId})
    )
  ''');
  }

  Future<void> _createSettingsTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${DatabaseConstants.tableSettings} (
        ${DatabaseConstants.columnSettingKey} TEXT PRIMARY KEY,
        ${DatabaseConstants.columnSettingValue} TEXT NOT NULL,
        ${DatabaseConstants.columnSettingType} TEXT DEFAULT 'string',
        ${DatabaseConstants.columnSettingDescription} TEXT,
        ${DatabaseConstants.columnSettingUpdatedAt} TEXT NOT NULL,
        ${DatabaseConstants.columnSettingIsSynced} INTEGER DEFAULT 0
      )
    ''');
  }

  Future<void> _createSyncLogTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${DatabaseConstants.tableSyncLog} (
        ${DatabaseConstants.columnSyncLogId} TEXT PRIMARY KEY,
        ${DatabaseConstants.columnSyncLogTableName} TEXT,
        ${DatabaseConstants.columnSyncLogOperation} TEXT,
        ${DatabaseConstants.columnSyncLogRecordId} TEXT,
        ${DatabaseConstants.columnSyncLogStatus} INTEGER DEFAULT 0,
        ${DatabaseConstants.columnSyncLogErrorMessage} TEXT,
        ${DatabaseConstants.columnSyncLogCreatedAt} TEXT,
        ${DatabaseConstants.columnSyncLogSyncedAt} TEXT
      )
    ''');
  }

  // Create Views (updated with DatabaseConstants)
  Future<void> _createViews(Database db) async {
    // Daily sales summary
    await db.execute('''
      CREATE VIEW view_daily_sales_summary AS
      SELECT
        DATE(${DatabaseConstants.columnSaleSaleDate}) AS day,
        COUNT(*) AS total_invoices,
        SUM(${DatabaseConstants.columnSaleTotalAmount}) AS total_sales,
        SUM(${DatabaseConstants.columnSalePaidAmount}) AS total_paid,
        SUM(${DatabaseConstants.columnSaleTotalAmount} - ${DatabaseConstants.columnSalePaidAmount}) AS total_due
      FROM ${DatabaseConstants.tableSales}
      WHERE ${DatabaseConstants.columnSaleDeletedAt} IS NULL
      GROUP BY DATE(${DatabaseConstants.columnSaleSaleDate})
      ORDER BY DATE(${DatabaseConstants.columnSaleSaleDate}) DESC
    ''');

    // Customer account statement
    await db.execute('''
      CREATE VIEW view_customer_account_statement AS
      SELECT
        c.${DatabaseConstants.columnCustomerId} AS customer_id,
        c.${DatabaseConstants.columnCustomerName} AS customer_name,
        t.${DatabaseConstants.columnTransactionId} AS transaction_id,
        t.${DatabaseConstants.columnTransactionTransactionDate} AS transaction_date,
        t.${DatabaseConstants.columnTransactionAmount} AS amount,
        t.${DatabaseConstants.columnTransactionType} AS type,
        t.${DatabaseConstants.columnTransactionSubType} AS sub_type,
        t.${DatabaseConstants.columnTransactionNotes} AS notes
      FROM ${DatabaseConstants.tableCustomers} c
      LEFT JOIN ${DatabaseConstants.tableTransactions} t ON t.${DatabaseConstants.columnTransactionCustomerId} = c.${DatabaseConstants.columnCustomerId}
      WHERE c.${DatabaseConstants.columnCustomerDeletedAt} IS NULL
      ORDER BY c.${DatabaseConstants.columnCustomerName}, t.${DatabaseConstants.columnTransactionTransactionDate}
    ''');

    // Add more views as needed...
  }

  // Insert default data (updated with DatabaseConstants)
  Future<void> _insertDefaultData(Database db) async {
    try {
      final now = DateTime.now().toIso8601String();

      // Insert default branch
      await db.insert(DatabaseConstants.tableBranches, {
        DatabaseConstants.columnBranchId: AppUtils.generateId(),
        DatabaseConstants.columnBranchName: AppConstants.defaultBranchName,
        DatabaseConstants.columnBranchAddress: '',
        DatabaseConstants.columnBranchCreatedAt: now,
        DatabaseConstants.columnBranchUpdatedAt: now,
        DatabaseConstants.columnBranchIsSynced: 0,
      });

      // Insert default category
      await db.insert(DatabaseConstants.tableCategories, {
        DatabaseConstants.columnCategoryId: AppUtils.generateId(),
        DatabaseConstants.columnCategoryNameAr:
            AppConstants.defaultCategoryName,
        DatabaseConstants.columnCategoryNameEn: 'General',
        DatabaseConstants.columnCategoryDescription: 'تصنيف افتراضي',
        DatabaseConstants.columnCategoryIsActive: 1,
        DatabaseConstants.columnCategorySortOrder: 1,
        DatabaseConstants.columnCategoryCreatedAt: now,
        DatabaseConstants.columnCategoryUpdatedAt: now,
        DatabaseConstants.columnCategoryIsSynced: 0,
      });

      // Insert default unit
      await db.insert(DatabaseConstants.tableUnits, {
        DatabaseConstants.columnUnitId: AppUtils.generateId(),
        DatabaseConstants.columnUnitName: AppConstants.defaultUnitName,
        DatabaseConstants.columnUnitSymbol: 'قطعة',
        DatabaseConstants.columnUnitAbbreviation: 'قطعة',
        DatabaseConstants.columnUnitFactor: 1.0,
        DatabaseConstants.columnUnitType: 'عدد',
        DatabaseConstants.columnUnitIsBaseUnit: 1,
        DatabaseConstants.columnUnitIsActive: 1,
        DatabaseConstants.columnUnitDescription: 'وحدة افتراضية',
        DatabaseConstants.columnUnitCreatedAt: now,
        DatabaseConstants.columnUnitUpdatedAt: now,
        DatabaseConstants.columnUnitIsSynced: 0,
      });

      // Insert default customer
      await db.insert(DatabaseConstants.tableCustomers, {
        DatabaseConstants.columnCustomerId: AppUtils.generateId(),
        DatabaseConstants.columnCustomerName: AppConstants.defaultCustomerName,
        DatabaseConstants.columnCustomerPhone: '',
        DatabaseConstants.columnCustomerEmail: '',
        DatabaseConstants.columnCustomerBalance: 0.0,
        DatabaseConstants.columnCustomerCreatedAt: now,
        DatabaseConstants.columnCustomerUpdatedAt: now,
        DatabaseConstants.columnCustomerIsSynced: 0,
      });

      // Insert default supplier
      await db.insert(DatabaseConstants.tableSuppliers, {
        DatabaseConstants.columnSupplierId: AppUtils.generateId(),
        DatabaseConstants.columnSupplierName: AppConstants.defaultSupplierName,
        DatabaseConstants.columnSupplierPhone: '',
        DatabaseConstants.columnSupplierEmail: '',
        DatabaseConstants.columnSupplierBalance: 0.0,
        DatabaseConstants.columnSupplierCreatedAt: now,
        DatabaseConstants.columnSupplierUpdatedAt: now,
        DatabaseConstants.columnSupplierIsSynced: 0,
      });

      // Insert default cash box
      await db.insert(DatabaseConstants.tableCashBoxes, {
        DatabaseConstants.columnCashBoxId: AppUtils.generateId(),
        DatabaseConstants.columnCashBoxName: AppConstants.defaultCashBoxName,
        DatabaseConstants.columnCashBoxBalance: 0.0,
        DatabaseConstants.columnCashBoxCreatedAt: now,
        DatabaseConstants.columnCashBoxUpdatedAt: now,
        DatabaseConstants.columnCashBoxIsSynced: 0,
      });

      // Insert default admin user
      await db.insert(DatabaseConstants.tableUsers, {
        DatabaseConstants.columnUserId: 'admin-user',
        DatabaseConstants.columnUserName: 'مدير النظام',
        DatabaseConstants.columnUserEmail: '<EMAIL>',
        DatabaseConstants.columnUserRole: 'مدير عام',
        DatabaseConstants.columnUserPermissions: 'admin',
        DatabaseConstants.columnUserIsActive: 1,
        DatabaseConstants.columnUserPasswordHash: 'admin123',
        DatabaseConstants.columnUserCreatedAt: now,
        DatabaseConstants.columnUserIsSynced: 0,
      });

      // Insert system user for automated operations
      await db.insert(DatabaseConstants.tableUsers, {
        DatabaseConstants.columnUserId: 'system',
        DatabaseConstants.columnUserName: 'النظام',
        DatabaseConstants.columnUserEmail: '<EMAIL>',
        DatabaseConstants.columnUserRole: 'نظام',
        DatabaseConstants.columnUserPermissions: 'system',
        DatabaseConstants.columnUserIsActive: 1,
        DatabaseConstants.columnUserPasswordHash: '',
        DatabaseConstants.columnUserCreatedAt: now,
        DatabaseConstants.columnUserIsSynced: 0,
      });

      // Insert default warehouse
      await db.insert(DatabaseConstants.tableWarehouses, {
        DatabaseConstants.columnWarehouseId: 'main-warehouse',
        DatabaseConstants.columnWarehouseName: 'المخزن الرئيسي',
        DatabaseConstants.columnWarehouseLocation: 'الفرع الرئيسي',
        'address': 'العنوان الرئيسي للشركة',
        DatabaseConstants.columnWarehouseManagerId: 'admin-user',
        'phone': '+966123456789',
        'email': '<EMAIL>',
        DatabaseConstants.columnWarehouseIsActive: 1,
        'is_default': 1,
        'description': 'المخزن الرئيسي للشركة',
        'warehouse_type': 'main',
        'capacity': 10000.0,
        DatabaseConstants.columnWarehouseCreatedAt: now,
        DatabaseConstants.columnWarehouseUpdatedAt: now,
        DatabaseConstants.columnWarehouseIsSynced: 0,
      });

      // Insert default settings
      final defaultSettings = {
        'company_name': 'شركة تجاري تك المحدودة',
        'company_address': 'الرياض، المملكة العربية السعودية',
        'company_phone': '0112345678',
        'company_email': '<EMAIL>',
        'tax_rate': '15',
        'currency': 'SAR',
        'currency_symbol': 'ر.س',
        'date_format': 'dd/MM/yyyy',
        'time_format': '24h',
        'language': 'ar',
        'backup_frequency': 'daily',
        'auto_backup': 'true',
      };

      for (final entry in defaultSettings.entries) {
        await db.insert(DatabaseConstants.tableSettings, {
          DatabaseConstants.columnSettingKey: entry.key,
          DatabaseConstants.columnSettingValue: entry.value,
          DatabaseConstants.columnSettingType: 'string',
          DatabaseConstants.columnSettingUpdatedAt: now,
          DatabaseConstants.columnSettingIsSynced: 0,
        });
      }

      AppUtils.logInfo('Default data inserted successfully');
    } catch (e) {
      AppUtils.logError('Error inserting default data', e);
    }
  }

  Future<void> _createDefaultUnits(Database db) async {
    // إنشاء وحدات الوزن
    await db.insert(DatabaseConstants.tableUnits, {
      DatabaseConstants.columnUnitId: 'unit_gram',
      DatabaseConstants.columnUnitName: 'جرام',
      DatabaseConstants.columnUnitSymbol: 'جم',
      DatabaseConstants.columnUnitFactor: 1.0,
      DatabaseConstants.columnUnitType: 'weight',
      DatabaseConstants.columnUnitIsBaseUnit: 1,
      DatabaseConstants.columnUnitIsActive: 1,
      DatabaseConstants.columnUnitCreatedAt: DateTime.now().toIso8601String(),
    });

    await db.insert(DatabaseConstants.tableUnits, {
      DatabaseConstants.columnUnitId: 'unit_kg',
      DatabaseConstants.columnUnitName: 'كيلوجرام',
      DatabaseConstants.columnUnitSymbol: 'كجم',
      DatabaseConstants.columnUnitFactor: 1000.0,
      DatabaseConstants.columnUnitBaseUnitId: 'unit_gram',
      DatabaseConstants.columnUnitType: 'weight',
      DatabaseConstants.columnUnitIsBaseUnit: 0,
      DatabaseConstants.columnUnitIsActive: 1,
      DatabaseConstants.columnUnitCreatedAt: DateTime.now().toIso8601String(),
    });

    // إنشاء وحدات الطول
    await db.insert(DatabaseConstants.tableUnits, {
      DatabaseConstants.columnUnitId: 'unit_cm',
      DatabaseConstants.columnUnitName: 'سنتيمتر',
      DatabaseConstants.columnUnitSymbol: 'سم',
      DatabaseConstants.columnUnitFactor: 1.0,
      DatabaseConstants.columnUnitType: 'length',
      DatabaseConstants.columnUnitIsBaseUnit: 1,
      DatabaseConstants.columnUnitIsActive: 1,
      DatabaseConstants.columnUnitCreatedAt: DateTime.now().toIso8601String(),
    });

    await db.insert(DatabaseConstants.tableUnits, {
      DatabaseConstants.columnUnitId: 'unit_meter',
      DatabaseConstants.columnUnitName: 'متر',
      DatabaseConstants.columnUnitSymbol: 'م',
      DatabaseConstants.columnUnitFactor: 100.0,
      DatabaseConstants.columnUnitBaseUnitId: 'unit_cm',
      DatabaseConstants.columnUnitType: 'length',
      DatabaseConstants.columnUnitIsBaseUnit: 0,
      DatabaseConstants.columnUnitIsActive: 1,
      DatabaseConstants.columnUnitCreatedAt: DateTime.now().toIso8601String(),
    });

    // إنشاء وحدات الحجم
    await db.insert(DatabaseConstants.tableUnits, {
      DatabaseConstants.columnUnitId: 'unit_ml',
      DatabaseConstants.columnUnitName: 'مليلتر',
      DatabaseConstants.columnUnitSymbol: 'مل',
      DatabaseConstants.columnUnitFactor: 1.0,
      DatabaseConstants.columnUnitType: 'volume',
      DatabaseConstants.columnUnitIsBaseUnit: 1,
      DatabaseConstants.columnUnitIsActive: 1,
      DatabaseConstants.columnUnitCreatedAt: DateTime.now().toIso8601String(),
    });

    await db.insert(DatabaseConstants.tableUnits, {
      DatabaseConstants.columnUnitId: 'unit_liter',
      DatabaseConstants.columnUnitName: 'لتر',
      DatabaseConstants.columnUnitSymbol: 'لتر',
      DatabaseConstants.columnUnitFactor: 1000.0,
      DatabaseConstants.columnUnitBaseUnitId: 'unit_ml',
      DatabaseConstants.columnUnitType: 'volume',
      DatabaseConstants.columnUnitIsBaseUnit: 0,
      DatabaseConstants.columnUnitIsActive: 1,
      DatabaseConstants.columnUnitCreatedAt: DateTime.now().toIso8601String(),
    });

    // إنشاء وحدة العدد
    await db.insert(DatabaseConstants.tableUnits, {
      DatabaseConstants.columnUnitId: 'unit_piece',
      DatabaseConstants.columnUnitName: 'قطعة',
      DatabaseConstants.columnUnitSymbol: 'قطعة',
      DatabaseConstants.columnUnitFactor: 1.0,
      DatabaseConstants.columnUnitType: 'count',
      DatabaseConstants.columnUnitIsBaseUnit: 1,
      DatabaseConstants.columnUnitIsActive: 1,
      DatabaseConstants.columnUnitCreatedAt: DateTime.now().toIso8601String(),
    });
  }

  // Drop all tables (for upgrades)
  Future<void> _dropAllTables(Database db) async {
    final tables = [
      DatabaseConstants.tableSyncLog,
      DatabaseConstants.tableSettings,
      DatabaseConstants.tableAuditLog,
      DatabaseConstants.tableAiInsights,
      DatabaseConstants.tableAiPredictions,
      DatabaseConstants.tableAttachments,
      DatabaseConstants.tableDeviceSessions,
      DatabaseConstants.tableNotifications,
      DatabaseConstants.tableTransactions,
      DatabaseConstants.tableBankAccounts,
      DatabaseConstants.tableCashBoxes,
      DatabaseConstants.tableSaleItems,
      DatabaseConstants.tableSales,
      DatabaseConstants.tablePurchaseItems,
      DatabaseConstants.tablePurchases,
      DatabaseConstants.tableSuppliers,
      DatabaseConstants.tableCustomers,
      DatabaseConstants.tableStockAdjustmentItems,
      DatabaseConstants.tableStockAdjustments,
      DatabaseConstants.tableStockMovements,
      DatabaseConstants.tableStocks,
      DatabaseConstants.tableWarehouses,
      DatabaseConstants.tableProductPrices,
      DatabaseConstants.tableProducts,
      DatabaseConstants.tableUnits,
      DatabaseConstants.tableCategories,
      DatabaseConstants.tableSalaries,
      DatabaseConstants.tableEmployees,
      DatabaseConstants.tableJournalEntries,
      DatabaseConstants.tableUsers,
      DatabaseConstants.tableBranches
    ];

    for (final table in tables) {
      await db.execute('DROP TABLE IF EXISTS $table');
    }
  }

//
  // Close database
  Future<void> close() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }
  }

  /// إعادة تعيين قاعدة البيانات في حالة حدوث مشاكل
  Future<void> resetDatabase() async {
    try {
      AppUtils.logInfo('بدء إعادة تعيين قاعدة البيانات...');

      // إغلاق قاعدة البيانات الحالية
      await close();

      // حذف ملف قاعدة البيانات
      await deleteDatabase();

      // إعادة تهيئة قاعدة البيانات
      _database = await _initDatabase();

      AppUtils.logInfo('تم إعادة تعيين قاعدة البيانات بنجاح');
    } catch (e) {
      AppUtils.logError('خطأ في إعادة تعيين قاعدة البيانات', e);
      rethrow;
    }
  }

  // Delete database file
  Future<void> deleteDatabase() async {
    try {
      final documentsDirectory = await getApplicationDocumentsDirectory();
      final path = join(documentsDirectory.path, AppConstants.databaseName);
      final file = File(path);

      if (await file.exists()) {
        await file.delete();
        AppUtils.logInfo('Database file deleted');
      }

      _database = null;
    } catch (e) {
      AppUtils.logError('Error deleting database', e);
    }
  }

  // Get database size
  Future<int> getDatabaseSize() async {
    try {
      final documentsDirectory = await getApplicationDocumentsDirectory();
      final path = join(documentsDirectory.path, AppConstants.databaseName);
      final file = File(path);

      if (await file.exists()) {
        return await file.length();
      }

      return 0;
    } catch (e) {
      AppUtils.logError('Error getting database size', e);
      return 0;
    }
  }
}
